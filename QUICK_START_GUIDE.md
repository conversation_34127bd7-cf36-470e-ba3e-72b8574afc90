# Quick Start Guide: iOS App Publishing

## Prerequisites Checklist

### ✅ Required Accounts

- [ ] **Apple Developer Account** ($99/year) - [Sign up here](https://developer.apple.com)
- [ ] **App Store Connect Access** - Linked to your developer account

### ✅ Development Environment

- [ ] **Xcode** installed (latest version)
- [ ] **Node.js** and **npm/yarn** for React Native
- [ ] **Cocoapods** installed (`sudo gem install cocoapods`)

## Step 1: Prepare Your Development Environment

### Install Dependencies

```bash
# Install iOS dependencies
cd ios && pod install && cd ..

# Make build script executable
chmod +x scripts/build-ios.sh
```

### Update Export Options

Edit `scripts/ExportOptions.plist`:

- Replace `YOUR_TEAM_ID_HERE` with your actual Team ID
- You can find your Team ID in [Apple Developer Portal](https://developer.apple.com/account)

## Step 2: Configure Code Signing in Xcode

1. **Open Xcode**: `open ios/SvgViewerApp.xcworkspace`
2. **Select Project**: In the navigator, select your project
3. **Signing & Capabilities**:

   - Select the "SvgViewerApp" target
   - Check "Automatically manage signing"
   - Select your development team
   - Xcode will automatically create certificates

4. **Verify Bundle Identifier**:
   - Ensure it matches your App ID in Apple Developer Portal
   - Format: `com.yourcompany.SvgViewerApp`

## Step 3: Build and Archive

### Option A: Using Script (Recommended)

```bash
# Clean and create archive
./scripts/build-ios.sh archive

# Or run full process (clean + archive + export)
./scripts/build-ios.sh full
```

### Option B: Using Xcode GUI

1. **Product** > **Clean Build Folder** (⇧⌘K)
2. **Product** > **Archive** (⌘B)
3. Wait for archive to complete
4. **Window** > **Organizer** to view archives

## Step 4: Upload to App Store Connect

### Using Xcode Organizer

1. Open **Window** > **Organizer**
2. Select your archive
3. Click **Distribute App**
4. Choose **App Store Connect**
5. Select **Upload**
6. Follow the signing process
7. Wait for upload to complete

## Step 5: App Store Connect Setup

### Create App Listing

1. Go to [App Store Connect](https://appstoreconnect.apple.com)
2. **My Apps** > **+** (Create New App)
3. Fill in:
   - Platform: iOS
   - Name: SvgViewerApp
   - Primary Language: English
   - Bundle ID: Select your App ID
   - SKU: SvgViewerApp-1.0.0

### Complete App Information

1. **App Information**:

   - Category: Utilities > Graphics & Design
   - Age Rating: 4+

2. **Pricing and Availability**:

   - Price: Free
   - Territories: All available

3. **Prepare for Submission**:
   - Select the uploaded build
   - Add app screenshots (see requirements below)
   - Complete description and metadata
   - Add support URLs

## Step 6: Screenshot Requirements

### Required Sizes (PNG format)

**iPhone:**

- 6.7-inch: 1290 × 2796px
- 6.5-inch: 1284 × 2778px
- 5.5-inch: 1242 × 2208px

**iPad:**

- 12.9-inch: 2048 × 2732px
- 11-inch: 1668 × 2388px

### Screenshot Scenarios

1. Home screen with file picker
2. SVG file viewing interface
3. Zoom functionality demonstration
4. Settings/features screen

## Step 7: Submit for Review

### Final Checklist Before Submission

- [ ] All metadata completed
- [ ] Screenshots uploaded for all device sizes
- [ ] App icon (1024x1024) uploaded
- [ ] Build version selected
- [ ] Privacy questionnaire completed
- [ ] Demo instructions added (if needed)

### Submit for Review

1. Click **Submit for Review**
2. Answer any final questions
3. Confirm submission

## Step 8: Monitor Review Process

### Expected Timeline

- **Review**: 24-48 hours typically
- **Status Updates**: Check email and App Store Connect
- **Possible Outcomes**:
  - ✅ Approved: App goes live (or scheduled)
  - ⚠️ Rejected: Address issues and resubmit
  - ℹ️ More Information: Respond to questions

### After Approval

- Monitor app performance
- Respond to user reviews
- Prepare for updates

## Troubleshooting Common Issues

### Code Signing Problems

- Verify Apple Developer membership is active
- Check certificates in Developer Portal
- Ensure automatic signing is enabled in Xcode

### Upload Failures

- Check internet connection
- Verify ExportOptions.plist team ID
- Try uploading through Xcode Organizer

### Metadata Rejections

- Follow App Store Review Guidelines
- Ensure all text is appropriate
- Provide clear functionality descriptions

## Support Resources

- **Apple Developer Support**: https://developer.apple.com/support/
- **App Store Review Guidelines**: https://developer.apple.com/app-store/review/guidelines/
- **React Native Documentation**: https://reactnative.dev/docs/publishing-to-app-store

## Emergency Contacts

- **Apple Developer Support**: +1-408-974-4897
- **App Store Connect Help**: https://help.apple.com/app-store-connect/

Good luck with your submission! 🚀
