# App Store Metadata Template for SvgViewerApp

## App Information

### Basic Details

- **App Name**: SvgViewerApp
- **Subtitle**: Professional SVG Viewer
- **Primary Category**: Utilities
- **Secondary Category**: Graphics & Design

### Version Information

- **Version Number**: 1.0.0
- **Build Number**: 1

## App Description

### English (Primary Language)

```
SvgViewerApp - Your Professional SVG Viewer for iOS

Open, view, and interact with SVG files seamlessly on your iPhone and iPad. SvgViewerApp provides crisp, high-quality rendering of vector graphics with smooth zooming, panning, and professional display capabilities.

✨ Key Features:
• High-quality SVG rendering with accurate colors and details
• Smooth zoom and pan gestures for detailed inspection
• Support for standard SVG and compressed SVGZ files
• Clean, intuitive user interface designed for professionals
• Fast file loading and efficient memory usage
• Perfect for designers, developers, and creative professionals

Whether you're reviewing design mockups, checking vector assets, or simply viewing SVG files on the go, SvgViewerApp delivers a premium viewing experience.

Download now and experience the best SVG viewing on iOS!
```

### Promotional Text (Optional)

```
Experience professional SVG viewing on your iOS device. Perfect for designers and developers who work with vector graphics.
```

## Keywords

```
svg, viewer, vector, graphics, design, illustrator, sketch, drawing, image, professional, designer, developer, creative, artwork
```

## Support Information

- **Support URL**: https://yourwebsite.com/support
- **Marketing URL**: https://yourwebsite.com
- **Privacy Policy URL**: https://yourwebsite.com/privacy

## App Review Information

### Demo Account (If Required)

- **Username**: (Not required for this app)
- **Password**: (Not required for this app)

### Notes for Reviewers

```
This is an SVG file viewer application. No special configuration or accounts are needed for testing. Reviewers can use any SVG file to test the app's functionality. The app allows users to select SVG files from their device and view them with zoom and pan capabilities.
```

## Screenshot Requirements

### Required Sizes

**iPhone Screenshots:**

- 6.7-inch Display (1290 × 2796 pixels) - iPhone 15 Pro Max, 16 Pro Max
- 6.5-inch Display (1284 × 2778 pixels) - iPhone 12/13/14 Pro Max
- 5.5-inch Display (1242 × 2208 pixels) - iPhone 8 Plus, 7 Plus, 6s Plus

**iPad Screenshots:**

- 12.9-inch iPad Pro (2048 × 2732 pixels)
- 11-inch iPad Pro (1668 × 2388 pixels)
- 10.5-inch iPad Pro (1668 × 2224 pixels)

### Suggested Screenshot Scenarios

1. **Home Screen**: Show the file picker interface with instructions
2. **File Selection**: Demonstrate selecting an SVG file
3. **SVG Viewing**: Show a complex SVG being viewed with zoom controls
4. **Zoom Demonstration**: Display zoomed-in details of an SVG
5. **Settings/Info**: Show file information panel (if available)

### Screenshot Tips

- Use high-quality SVG files with interesting designs
- Show both simple and complex SVG examples
- Demonstrate the zoom functionality clearly
- Ensure text is readable in screenshots
- Use light and dark mode variants if available

## App Icon

- **Format**: PNG
- **Size**: 1024 × 1024 pixels
- **Requirements**: No transparency, square format

## Pricing and Availability

### Pricing

- **Price Tier**: Free
- **In-App Purchases**: None configured

### Availability

- **Countries**: All available territories
- **Release Date**: Immediately after approval

## Build Information

### Minimum Requirements

- **iOS Version**: 13.0 or later
- **Devices**: iPhone and iPad
- **Size**: Approximately 20-30MB

### Capabilities

- File access (document picker)
- No special permissions required

## Privacy Questionnaire

### Data Collection

- **Does the app collect any user data?**: No
- **Does the app use third-party analytics?**: No
- **Does the app show advertisements?**: Check your AdService configuration

### Data Usage

- **Files**: Only accessed when user explicitly selects them
- **No data is stored or transmitted to servers**

## Submission Checklist

### Before Uploading

- [ ] Test app thoroughly on physical devices
- [ ] Verify all functionality works as expected
- [ ] Check for any crashes or performance issues
- [ ] Ensure privacy compliance
- [ ] Prepare all metadata text
- [ ] Create all required screenshots
- [ ] Have 1024x1024 app icon ready

### After Upload

- [ ] Complete all App Store Connect metadata
- [ ] Select the correct build version
- [ ] Set pricing and availability
- [ ] Submit for review
- [ ] Monitor review status

## Support Resources

### Helpful Links

- [App Store Connect](https://appstoreconnect.apple.com)
- [Apple Developer Documentation](https://developer.apple.com/documentation)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)

### Contact Information

- **Developer Contact**: <EMAIL>
- **Support Contact**: <EMAIL>

Good luck with your App Store submission!
