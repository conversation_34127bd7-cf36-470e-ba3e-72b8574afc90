# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!
use_frameworks! :linkage => :static
$RNFirebaseAsStaticFramework = true
# Enable modular headers for Firebase compatibility
use_modular_headers!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'SvgViewerApp' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # Disable RNSVG resource bundles to fix archive issues
  pod 'RNSVG', :path => '../node_modules/react-native-svg', :modular_headers => true

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    # Fix for Xcode 16 compatibility and module import issues
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
        config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'
        config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS'] = 'YES'
        config.build_settings['VALIDATE_PRODUCT'] = 'NO'

        # Additional fixes for module import issues during archive
        config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'YES'
        config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
        config.build_settings['SWIFT_VERSION'] = '5.0'
        config.build_settings['LD_RUNPATH_SEARCH_PATHS'] = ['$(inherited)', '@executable_path/Frameworks']

        # Fix for privacy bundle Swift library copying issues
        if target.name.end_with?('_privacy') || target.name.include?('_Privacy') || target.name.include?('privacy')
          config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'NO'
          config.build_settings['SWIFT_STDLIB_TOOL_INVOCATION_FORCE_UNIX_PATH_SEPARATOR'] = 'YES'
          config.build_settings['SKIP_INSTALL'] = 'YES'
        end

        # Fix for UIKit and system framework import issues
        if target.name == 'SvgViewerApp'
          config.build_settings['FRAMEWORK_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/System/Library/Frameworks']
          config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/usr/lib']
          config.build_settings['HEADER_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/usr/include']
        end
      end
    end
    
    # Additional fix for React Native modules
    installer.pods_project.build_configurations.each do |config|
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
    end

    # Remove CopySwiftLibs build phase from privacy bundle targets and problematic bundles
    installer.pods_project.targets.each do |target|
      if target.name.end_with?('_privacy') || target.name.include?('_Privacy') || target.name.include?('privacy') || target.name.include?('RNSVGFilters') || target.name.include?('_resources')
        target.build_phases.each do |build_phase|
          if build_phase.respond_to?(:shell_script) && build_phase.shell_script.include?('copy_swift_stdlib')
            target.build_phases.delete(build_phase)
          end
          # Remove CopySwiftLibs build phase entirely
          if build_phase.class.to_s.include?('CopySwiftLibs') || build_phase.display_name == 'Copy Swift Standard Libraries'
            target.build_phases.delete(build_phase)
          end
        end
      end

      # Completely disable problematic bundle targets
      problematic_bundles = [
        'RNSVG-RNSVGFilters',
        'RNCAsyncStorage-RNCAsyncStorage_resources',
        'GoogleUserMessagingPlatform-UserMessagingPlatformResources',
        'Google-Mobile-Ads-SDK-GoogleMobileAdsResources'
      ]

      if problematic_bundles.include?(target.name)
        target.build_configurations.each do |config|
          config.build_settings['SKIP_INSTALL'] = 'YES'
          config.build_settings['EXCLUDED_ARCHS'] = 'arm64 x86_64'
          config.build_settings['VALID_ARCHS'] = ''
          config.build_settings['SUPPORTED_PLATFORMS'] = ''
        end
        # Remove all build phases from problematic bundle targets
        target.build_phases.clear
      end
    end

    # Remove problematic bundle targets entirely from the project
    problematic_bundles = [
      'RNSVG-RNSVGFilters',
      'RNCAsyncStorage-RNCAsyncStorage_resources',
      'GoogleUserMessagingPlatform-UserMessagingPlatformResources',
      'Google-Mobile-Ads-SDK-GoogleMobileAdsResources'
    ]
    installer.pods_project.targets.delete_if { |target| problematic_bundles.include?(target.name) }

    # Clean up resource script references to removed bundles (run after script generation)
    installer.pods_project.targets.each do |target|
      if target.name == 'Pods-SvgViewerApp'
        resource_script_path = File.join(installer.sandbox.root, 'Target Support Files', 'Pods-SvgViewerApp', 'Pods-SvgViewerApp-resources.sh')
        if File.exist?(resource_script_path)
          resource_script_content = File.read(resource_script_path)
          # Remove lines referencing the problematic bundles
          resource_script_content.gsub!(/.*GoogleMobileAdsResources\.bundle.*\n/, '')
          resource_script_content.gsub!(/.*UserMessagingPlatformResources\.bundle.*\n/, '')
          resource_script_content.gsub!(/.*RNCAsyncStorage_resources\.bundle.*\n/, '')
          resource_script_content.gsub!(/.*RNSVGFilters\.bundle.*\n/, '')
          File.write(resource_script_path, resource_script_content)
          puts "Cleaned up resource script references to removed bundles"
        end
        break
      end
    end
  end
end
