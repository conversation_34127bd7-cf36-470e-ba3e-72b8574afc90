#!/bin/bash

# Hermes dSYM Fix Script
# This script copies Hermes dSYM files to the archive's dSYMs folder
# to ensure proper crash reporting and debugging support

set -e

echo "🔧 Starting Hermes dSYM fix..."
echo "📊 Environment: CONFIGURATION=$CONFIGURATION"
echo "📊 Archive paths: ARCHIVE_PRODUCTS_PATH=$ARCHIVE_PRODUCTS_PATH, ARCHIVE_PATH=$ARCHIVE_PATH"
echo "📊 Build settings: BUILT_PRODUCTS_DIR=$BUILT_PRODUCTS_DIR"

# Only run this script during archive builds
if [ "${CONFIGURATION}" != "Release" ]; then
    echo "⚠️  Skipping Hermes dSYM fix - not a Release build"
    exit 0
fi

# Check if we're in an archive build (be more flexible about detection)
if [ -z "${ARCHIVE_PRODUCTS_PATH}" ] && [ -z "${ARCHIVE_PATH}" ] && [[ "$BUILT_PRODUCTS_DIR" != *"ArchiveIntermediates"* ]]; then
    echo "⚠️  Skipping Hermes dSYM fix - not an archive build"
    echo "📊 Debug: ARCHIVE_PRODUCTS_PATH='${ARCHIVE_PRODUCTS_PATH}', ARCHIVE_PATH='${ARCHIVE_PATH}'"
    echo "📊 Debug: BUILT_PRODUCTS_DIR='${BUILT_PRODUCTS_DIR}'"
    exit 0
fi

# Define paths - try multiple locations for Hermes framework
HERMES_FRAMEWORK_PATHS=(
    "${BUILT_PRODUCTS_DIR}/hermes.framework"
    "${BUILT_PRODUCTS_DIR}/XCFrameworkIntermediates/hermes-engine/Pre-built/hermes.framework"
    "${BUILT_PRODUCTS_DIR}/../XCFrameworkIntermediates/hermes-engine/Pre-built/hermes.framework"
)

HERMES_FRAMEWORK_PATH=""
for path in "${HERMES_FRAMEWORK_PATHS[@]}"; do
    if [ -d "$path" ]; then
        HERMES_FRAMEWORK_PATH="$path"
        break
    fi
done

HERMES_DSYM_PATH="${BUILT_PRODUCTS_DIR}/hermes.framework.dSYM"

# Determine archive dSYMs path
if [ -n "${ARCHIVE_DSYMS_PATH}" ]; then
    ARCHIVE_DSYMS_PATH="${ARCHIVE_DSYMS_PATH}"
elif [ -n "${ARCHIVE_PATH}" ]; then
    ARCHIVE_DSYMS_PATH="${ARCHIVE_PATH}/dSYMs"
elif [ -n "${ARCHIVE_PRODUCTS_PATH}" ]; then
    ARCHIVE_DSYMS_PATH="${ARCHIVE_PRODUCTS_PATH}/../dSYMs"
else
    # Fallback: try to detect from BUILT_PRODUCTS_DIR
    if [[ "$BUILT_PRODUCTS_DIR" == *"ArchiveIntermediates"* ]]; then
        ARCHIVE_BASE=$(echo "$BUILT_PRODUCTS_DIR" | sed 's|/BuildProductsPath.*||')
        ARCHIVE_DSYMS_PATH="${ARCHIVE_BASE}/dSYMs"
    else
        echo "❌ Could not determine archive dSYMs path"
        exit 1
    fi
fi

echo "📍 Looking for Hermes framework at: ${HERMES_FRAMEWORK_PATH}"
echo "📍 Looking for Hermes dSYM at: ${HERMES_DSYM_PATH}"
echo "📍 Archive dSYMs path: ${ARCHIVE_DSYMS_PATH}"

# Check if Hermes framework exists
if [ -z "${HERMES_FRAMEWORK_PATH}" ] || [ ! -d "${HERMES_FRAMEWORK_PATH}" ]; then
    echo "❌ Hermes framework not found in any expected location:"
    for path in "${HERMES_FRAMEWORK_PATHS[@]}"; do
        echo "   - $path"
    done
    exit 1
fi

echo "✅ Found Hermes framework at: ${HERMES_FRAMEWORK_PATH}"

# Check if Hermes dSYM exists
if [ ! -d "${HERMES_DSYM_PATH}" ]; then
    echo "⚠️  Hermes dSYM not found at ${HERMES_DSYM_PATH}"

    # Try alternative locations
    ALTERNATIVE_DSYM_PATHS=(
        "${CONFIGURATION_BUILD_DIR}/hermes.framework.dSYM"
        "${TARGET_BUILD_DIR}/hermes.framework.dSYM"
        "${PODS_CONFIGURATION_BUILD_DIR}/hermes-engine/hermes.framework.dSYM"
        "${BUILT_PRODUCTS_DIR}/../XCFrameworkIntermediates/hermes-engine/Pre-built/hermes.framework.dSYM"
    )

    for alt_path in "${ALTERNATIVE_DSYM_PATHS[@]}"; do
        echo "🔍 Checking alternative path: ${alt_path}"
        if [ -d "${alt_path}" ]; then
            HERMES_DSYM_PATH="${alt_path}"
            echo "✅ Found Hermes dSYM at: ${HERMES_DSYM_PATH}"
            break
        fi
    done

    if [ ! -d "${HERMES_DSYM_PATH}" ]; then
        echo "⚠️  No pre-built Hermes dSYM found. This is normal for React Native 0.79+ with pre-built Hermes."
        echo "🔧 Creating dSYM from Hermes framework binary..."

        # For React Native 0.79+, Hermes is distributed as a pre-built framework without dSYM
        # We need to create a dSYM structure manually
        TEMP_DSYM_PATH="${BUILT_PRODUCTS_DIR}/hermes.framework.dSYM"
        mkdir -p "${TEMP_DSYM_PATH}/Contents/Resources/DWARF"

        # Create Info.plist for the dSYM
        cat > "${TEMP_DSYM_PATH}/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>English</string>
    <key>CFBundleIdentifier</key>
    <string>com.apple.xcode.dsym.hermes.framework</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>dSYM</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
</dict>
</plist>
EOF

        # Copy the Hermes binary as the DWARF file (even though it may not have debug symbols)
        # This ensures the UUID matches and crash reports can at least identify the framework
        if [ -f "${HERMES_FRAMEWORK_PATH}/hermes" ]; then
            cp "${HERMES_FRAMEWORK_PATH}/hermes" "${TEMP_DSYM_PATH}/Contents/Resources/DWARF/hermes"
            echo "✅ Created dSYM structure with Hermes binary"
            HERMES_DSYM_PATH="${TEMP_DSYM_PATH}"
        else
            echo "❌ Could not find Hermes binary at ${HERMES_FRAMEWORK_PATH}/hermes"
            exit 1
        fi
    fi
fi

# Create archive dSYMs directory if it doesn't exist
if [ ! -d "${ARCHIVE_DSYMS_PATH}" ]; then
    echo "📁 Creating archive dSYMs directory: ${ARCHIVE_DSYMS_PATH}"
    mkdir -p "${ARCHIVE_DSYMS_PATH}"
fi

# Copy Hermes dSYM to archive
DEST_DSYM_PATH="${ARCHIVE_DSYMS_PATH}/hermes.framework.dSYM"
echo "📋 Copying Hermes dSYM to archive..."
echo "   From: ${HERMES_DSYM_PATH}"
echo "   To: ${DEST_DSYM_PATH}"

if [ -d "${DEST_DSYM_PATH}" ]; then
    echo "🗑️  Removing existing dSYM at destination"
    rm -rf "${DEST_DSYM_PATH}"
fi

cp -R "${HERMES_DSYM_PATH}" "${DEST_DSYM_PATH}"

# Verify the copy was successful
if [ -d "${DEST_DSYM_PATH}" ]; then
    echo "✅ Successfully copied Hermes dSYM to archive"
    
    # Check for DWARF file
    DWARF_PATH="${DEST_DSYM_PATH}/Contents/Resources/DWARF/hermes"
    if [ -f "${DWARF_PATH}" ]; then
        echo "✅ DWARF file found at: ${DWARF_PATH}"
        
        # Get UUID of the DWARF file
        UUID=$(dwarfdump --uuid "${DWARF_PATH}" 2>/dev/null | head -1 | awk '{print $2}' || echo "unknown")
        echo "🔍 Hermes DWARF UUID: ${UUID}"
    else
        echo "⚠️  DWARF file not found at expected location: ${DWARF_PATH}"
    fi
else
    echo "❌ Failed to copy Hermes dSYM to archive"
    exit 1
fi

echo "🎉 Hermes dSYM fix completed successfully!"
