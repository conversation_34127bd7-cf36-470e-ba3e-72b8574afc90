#!/bin/bash

# Hermes dSYM Post-Archive Script
# This script ensures that hermes.framework dSYM files are included in the archive after it's created

set -e

echo "Hermes dSYM Post-Archive: Starting..."

# Path to the archive that was just created
ARCHIVE_PATH="$1"
if [ -z "$ARCHIVE_PATH" ]; then
    echo "Error: No archive path provided"
    exit 1
fi

echo "Hermes dSYM Post-Archive: Processing archive at $ARCHIVE_PATH"

# Path to hermes framework in the archive
HERMES_FRAMEWORK_PATH="$ARCHIVE_PATH/Products/Applications/${TARGET_NAME}.app/Frameworks/hermes.framework"
HERMES_DSYM_SOURCE_PATH="${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework.dSYM"
HERMES_DSYM_DEST_PATH="$ARCHIVE_PATH/dSYMs/hermes.framework.dSYM"

# Check if hermes framework exists in the archive
if [ -d "$HERMES_FRAMEWORK_PATH" ]; then
    echo "Hermes dSYM Post-Archive: Found hermes.framework in archive"
    
    # Check if hermes dSYM already exists in the archive
    if [ -d "$HERMES_DSYM_DEST_PATH" ]; then
        echo "Hermes dSYM Post-Archive: hermes.framework.dSYM already exists in archive"
    else
        echo "Hermes dSYM Post-Archive: hermes.framework.dSYM not found in archive, checking source..."
        
        # Check if hermes dSYM exists in Pods
        if [ -d "$HERMES_DSYM_SOURCE_PATH" ]; then
            echo "Hermes dSYM Post-Archive: Found hermes.framework.dSYM in Pods, copying to archive..."
            
            # Ensure dSYMs directory exists
            mkdir -p "$ARCHIVE_PATH/dSYMs"
            
            # Copy the dSYM to the archive directory
            cp -R "$HERMES_DSYM_SOURCE_PATH" "$ARCHIVE_PATH/dSYMs/"
            
            # Verify the copy was successful
            if [ -d "$HERMES_DSYM_DEST_PATH" ]; then
                echo "Hermes dSYM Post-Archive: Successfully copied hermes.framework.dSYM to archive"
                
                # Get UUID of the hermes binary to verify it matches
                HERMES_BINARY_PATH="$HERMES_FRAMEWORK_PATH/hermes"
                if [ -f "$HERMES_BINARY_PATH" ]; then
                    UUID=$(dwarfdump -u "$HERMES_BINARY_PATH" | awk '{print $2}')
                    echo "Hermes dSYM Post-Archive: Hermes binary UUID: $UUID"
                    
                    # Verify dSYM contains the expected UUID
                    DSYM_UUID=$(dwarfdump -u "$HERMES_DSYM_DEST_PATH" | grep "$UUID" || true)
                    if [ ! -z "$DSYM_UUID" ]; then
                        echo "Hermes dSYM Post-Archive: dSYM verification successful - contains expected UUID"
                    else
                        echo "Hermes dSYM Post-Archive: Warning - dSYM UUID verification failed"
                        dwarfdump -u "$HERMES_DSYM_DEST_PATH"
                    fi
                fi
            else
                echo "Hermes dSYM Post-Archive: Error - Failed to copy hermes.framework.dSYM"
            fi
        else
            echo "Hermes dSYM Post-Archive: Warning - hermes.framework.dSYM not found in Pods at $HERMES_DSYM_SOURCE_PATH"
            echo "Hermes dSYM Post-Archive: Searching for alternative hermes dSYM locations..."
            
            # Try to find hermes dSYM in other locations
            find "$PODS_ROOT" -name "hermes.framework.dSYM" -type d 2>/dev/null | while read dsym_path; do
                echo "Hermes dSYM Post-Archive: Found alternative dSYM at: $dsym_path"
                mkdir -p "$ARCHIVE_PATH/dSYMs"
                cp -R "$dsym_path" "$ARCHIVE_PATH/dSYMs/"
                if [ -d "$HERMES_DSYM_DEST_PATH" ]; then
                    echo "Hermes dSYM Post-Archive: Successfully copied hermes.framework.dSYM from alternative location"
                    break
                fi
            done
            
            # If still not found, try to generate dSYM from the framework
            if [ ! -d "$HERMES_DSYM_DEST_PATH" ] && [ -f "$HERMES_FRAMEWORK_PATH/hermes" ]; then
                echo "Hermes dSYM Post-Archive: Generating dSYM from hermes framework..."
                mkdir -p "$ARCHIVE_PATH/dSYMs"
                dsymutil "$HERMES_FRAMEWORK_PATH/hermes" -o "$HERMES_DSYM_DEST_PATH"
                
                if [ -d "$HERMES_DSYM_DEST_PATH" ]; then
                    echo "Hermes dSYM Post-Archive: Successfully generated hermes.framework.dSYM"
                else
                    echo "Hermes dSYM Post-Archive: Error - Failed to generate dSYM for hermes.framework"
                fi
            fi
        fi
    fi
else
    echo "Hermes dSYM Post-Archive: hermes.framework not found in archive, skipping dSYM fix"
fi

echo "Hermes dSYM Post-Archive: Completed"