import {FileSystem} from 'react-native-file-access';
import pako from 'pako';

/**
 * Checks if a file is an SVGZ file based on its extension
 * @param filePath Path to the file
 * @returns boolean indicating if the file is an SVGZ file
 */
export const isSvgzFile = (filePath: string): boolean => {
  return filePath.toLowerCase().endsWith('.svgz');
};

/**
 * Checks if a file is an SVG file based on its extension
 * @param filePath Path to the file
 * @returns boolean indicating if the file is an SVG file
 */
export const isSvgFile = (filePath: string): boolean => {
  return filePath.toLowerCase().endsWith('.svg');
};

/**
 * Detects if the content appears to be gzipped based on magic bytes
 * @param uint8Array The Uint8Array to check
 * @returns boolean indicating if the content appears to be gzipped
 */
export const isGzipped = (uint8Array: Uint8Array): boolean => {
  // Check for gzip magic bytes (1F 8B)
  return uint8Array.length >= 2 && uint8Array[0] === 0x1f && uint8Array[1] === 0x8b;
};

/**
 * Converts base64 string to Uint8Array
 * @param base64 Base64 encoded string
 * @returns Uint8Array containing the binary data
 */
const base64ToUint8Array = (base64: string): Uint8Array => {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
};

/**
 * Reads an SVG file and returns its content as a string
 * @param filePath Path to the SVG file
 * @returns Promise resolving to the SVG content as a string
 */
export const readSvgFile = async (filePath: string): Promise<string> => {
  return await FileSystem.readFile(filePath, 'utf8');
};

/**
 * Reads an SVGZ file, decompresses it, and returns its content as a string
 * @param filePath Path to the SVGZ file
 * @returns Promise resolving to the decompressed SVG content as a string
 */
export const readSvgzFile = async (filePath: string): Promise<string> => {
  // Read the file as a base64 encoded string
  const base64Data = await FileSystem.readFile(filePath, 'base64');

  // Convert base64 to Uint8Array
  const binaryData = base64ToUint8Array(base64Data);

  // Verify this is actually a gzipped file
  if (!isGzipped(binaryData)) {
    throw new Error('File is not in gzip format despite .svgz extension');
  }

  // Decompress the gzipped content using pako and convert directly to string
  try {
    // Use pako.inflate with { to: 'string' } option to get string directly
    const svgString = pako.inflate(binaryData, {to: 'string'});

    return svgString;
  } catch (error) {
    throw new Error(
      `Failed to decompress SVGZ file: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Reads and returns the content of an SVG or SVGZ file
 * @param filePath Path to the SVG or SVGZ file
 * @returns Promise resolving to the SVG content as a string
 */
export const readSvgContent = async (filePath: string): Promise<string> => {
  try {
    if (isSvgzFile(filePath)) {
      return await readSvgzFile(filePath);
    } else {
      // For regular SVG files
      return await readSvgFile(filePath);
    }
  } catch (error) {
    throw new Error(
      `Error reading SVG/SVGZ file: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Validates if the content is a valid SVG
 * @param content SVG content to validate
 * @returns boolean indicating if the content is valid SVG
 */
export const isValidSvgContent = (content: string): boolean => {
  return content.includes('<svg') && content.includes('</svg>');
};
