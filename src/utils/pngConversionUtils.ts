import {Alert, Platform, Dimensions} from 'react-native';
import {captureRef} from 'react-native-view-shot';
import {FileSystem, Dirs} from 'react-native-file-access';
import ViewShot from 'react-native-view-shot';

export async function savePng(viewShotRef: React.RefObject<ViewShot | null>, fileName: string) {
  if (!viewShotRef.current) return;

  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;

  try {
    // First, try the tmpfile approach with validation
    const success = await savePngWithTmpFile(viewShotRef, fileName, windowWidth, windowHeight);
    if (success) {
      return true;
    }

    // If tmpfile approach fails, fallback to base64 approach
    console.log('Tmpfile approach failed, trying base64 approach...');
    return await savePngWithBase64(viewShotRef, fileName, windowWidth, windowHeight);
  } catch (error) {
    console.error('PNG conversion error:', error);
    Alert.alert('Error', 'Failed to convert to PNG. Please try again.', [{text: 'OK'}]);

    return false;
  }
}

async function savePngWithTmpFile(
  viewShotRef: React.RefObject<ViewShot | null>,
  fileName: string,
  windowWidth: number,
  windowHeight: number,
): Promise<boolean> {
  try {
    const screenshotUri = await captureRef(viewShotRef, {
      format: 'png',
      quality: 1,
      result: 'tmpfile',
      height: windowHeight * 0.6 * 2,
      width: windowWidth * 0.9 * 2,
    });

    console.log('Screenshot captured at:', screenshotUri);

    // Validate that the temporary file exists before trying to copy it
    const fileExists = await FileSystem.exists(screenshotUri);
    if (!fileExists) {
      console.error('Temporary file does not exist at:', screenshotUri);
      return false;
    }

    const timestamp = new Date().getTime();
    const baseFileName = fileName.replace(/\.svg(z)?$/i, '');
    const pngFileName = `${baseFileName}_${timestamp}.png`;

    await FileSystem.cpExternal(screenshotUri, pngFileName, 'images');

    Alert.alert(
      'Success',
      Platform.select({
        android: 'PNG saved to your pictures folder',
        ios: 'PNG saved to app documents folder',
      }),
      [{text: 'OK'}],
    );

    return true;
  } catch (error) {
    console.error('TmpFile approach failed:', error);
    return false;
  }
}

async function savePngWithBase64(
  viewShotRef: React.RefObject<ViewShot | null>,
  fileName: string,
  windowWidth: number,
  windowHeight: number,
): Promise<boolean> {
  let tempPath: string | null = null;

  try {
    const base64Data = await captureRef(viewShotRef, {
      format: 'png',
      quality: 1,
      result: 'base64',
      height: windowHeight * 0.6 * 2,
      width: windowWidth * 0.9 * 2,
    });

    console.log('Screenshot captured as base64, length:', base64Data.length);

    const timestamp = new Date().getTime();
    const baseFileName = fileName.replace(/\.svg(z)?$/i, '');
    const pngFileName = `${baseFileName}_${timestamp}.png`;

    // Write the base64 data to a temporary file first, then copy to external storage
    tempPath = `${Dirs.CacheDir}/${pngFileName}`;
    await FileSystem.writeFile(tempPath, base64Data, 'base64');
    await FileSystem.cpExternal(tempPath, pngFileName, 'images');

    Alert.alert(
      'Success',
      Platform.select({
        android: 'PNG saved to your pictures folder',
        ios: 'PNG saved to app documents folder',
      }),
      [{text: 'OK'}],
    );

    return true;
  } catch (error) {
    console.error('Base64 approach failed:', error);
    throw error;
  } finally {
    // Clean up temporary file
    if (tempPath) {
      try {
        await FileSystem.unlink(tempPath);
        console.log('Temporary file cleaned up:', tempPath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temporary file:', cleanupError);
      }
    }
  }
}
