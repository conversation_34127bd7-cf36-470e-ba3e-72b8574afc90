import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Button from '@/components/Button';
import {useDispatch, useSelector} from 'react-redux';
import IapService from '@/services/IapService';
import {setPurchaseStatus} from '@/store/slices/iapSlice';
import {setRenderingEngine} from '@/store/slices/settingsSlice';
import type {RootState} from '@/store';
import type {RenderingEngine} from '@/store/slices/settingsSlice';
import {useTheme} from '@/theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const SettingsScreen = () => {
  const [isRestoring, setIsRestoring] = React.useState(false);
  const {hasSilver, hasGold} = useSelector((state: RootState) => state.iap);
  const {renderingEngine} = useSelector((state: RootState) => state.settings);
  console.log('SettingsScreen renderingEngine:', renderingEngine);
  console.log('SettingsScreen hasSilver:', hasSilver, 'hasGold:', hasGold);

  const dispatch = useDispatch();
  const theme = useTheme();

  const handleRestorePurchases = async () => {
    try {
      setIsRestoring(true);
      const purchases = await IapService.restorePurchases();
      const status = {
        hasSilver: false,
        hasGold: false,
      };

      purchases.forEach(purchase => {
        if (purchase.productId.includes('silver')) {
          status.hasSilver = true;
        }
        if (purchase.productId.includes('gold')) {
          status.hasGold = true;
        }
      });

      dispatch(setPurchaseStatus(status));

      Alert.alert(
        'Success',
        status.hasSilver || status.hasGold
          ? 'Your purchases have been restored!'
          : 'No previous purchases found.' + JSON.stringify(purchases),
        [{text: 'OK'}],
      );
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert('Error', 'Failed to restore purchases. Please try again.', [{text: 'OK'}]);
    } finally {
      setIsRestoring(false);
    }
  };

  const handleRenderingEngineChange = (engine: RenderingEngine) => {
    dispatch(setRenderingEngine(engine));
  };

  const getPlanIcon = () => {
    if (hasGold) return 'star';
    if (hasSilver) return 'star-half';
    return 'star-border';
  };

  const getPlanColor = () => {
    if (hasGold) return '#FFD700';
    if (hasSilver) return '#C0C0C0';
    return theme.colors.textTertiary;
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing[4],
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.xl,
      padding: theme.spacing[6],
      marginBottom: theme.spacing[4],
      ...theme.shadows.base,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing[4],
    },
    sectionIcon: {
      marginRight: theme.spacing[3],
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.textPrimary,
      flex: 1,
    },
    sectionDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing[4],
      lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.sm,
    },
    radioGroup: {
      gap: theme.spacing[2],
    },
    radioButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing[3],
      borderRadius: theme.borderRadius.md,
      borderWidth: 2,
      borderColor: 'transparent',
      backgroundColor: theme.colors.background,
    },
    radioButtonSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary + '10',
    },
    radio: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: theme.colors.border,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing[3],
    },
    radioSelected: {
      borderColor: theme.colors.primary,
    },
    radioSelectedInner: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
    },
    radioContent: {
      flex: 1,
    },
    radioText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing[1],
    },
    radioDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    statusCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing[4],
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing[4],
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    statusIcon: {
      marginRight: theme.spacing[3],
    },
    statusContent: {
      flex: 1,
    },
    statusTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.textPrimary,
      marginBottom: theme.spacing[1],
    },
    statusText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    buttonContainer: {
      marginTop: theme.spacing[2],
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing[4],
    },
    loadingText: {
      marginLeft: theme.spacing[2],
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Rendering Engine Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Icon
              name="settings"
              size={24}
              color={theme.colors.primary}
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>Rendering Engine</Text>
          </View>

          <Text style={styles.sectionDescription}>
            Choose how SVG files are rendered. Native provides better performance, while WebView
            offers better compatibility.
          </Text>

          <View style={styles.radioGroup}>
            <TouchableOpacity
              style={[
                styles.radioButton,
                renderingEngine === 'webview' && styles.radioButtonSelected,
              ]}
              onPress={() => handleRenderingEngineChange('webview')}>
              <View style={[styles.radio, renderingEngine === 'webview' && styles.radioSelected]}>
                {renderingEngine === 'webview' && <View style={styles.radioSelectedInner} />}
              </View>
              <View style={styles.radioContent}>
                <Text style={styles.radioText}>WebView Renderer</Text>
                <Text style={styles.radioDescription}>Better compatibility with complex SVGs</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.radioButton,
                renderingEngine === 'native' && styles.radioButtonSelected,
              ]}
              onPress={() => handleRenderingEngineChange('native')}>
              <View style={[styles.radio, renderingEngine === 'native' && styles.radioSelected]}>
                {renderingEngine === 'native' && <View style={styles.radioSelectedInner} />}
              </View>
              <View style={styles.radioContent}>
                <Text style={styles.radioText}>Native Renderer</Text>
                <Text style={styles.radioDescription}>Better performance and gesture support</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Subscription Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Icon
              name="card-membership"
              size={24}
              color={theme.colors.primary}
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>Subscription</Text>
          </View>

          <View style={styles.statusCard}>
            <Icon name={getPlanIcon()} size={32} color={getPlanColor()} style={styles.statusIcon} />
            <View style={styles.statusContent}>
              <Text style={styles.statusTitle}>
                Current Plan: {hasGold ? 'Gold' : hasSilver ? 'Silver' : 'Free'}
              </Text>
              <Text style={styles.statusText}>
                {hasGold
                  ? 'All features unlocked, no ads'
                  : hasSilver
                    ? 'PNG export enabled, no ads'
                    : 'Basic features with ads'}
              </Text>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            {isRestoring ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Restoring purchases...</Text>
              </View>
            ) : (
              <Button
                title="Restore Purchases"
                onPress={handleRestorePurchases}
                variant="outline"
              />
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default SettingsScreen;
