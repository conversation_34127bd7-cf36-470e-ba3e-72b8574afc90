import React from 'react';
import {View, ScrollView, TouchableOpacity, StyleProp, ViewStyle} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface SvgControlsProps {
  onConvertToPng: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  onRotateLeft?: () => void;
  onRotateRight?: () => void;
  onToggleFullscreen: () => void;
  onToggleLayerPanel: () => void;
  isFullscreen: boolean;
  layerPanelVisible: boolean;
  hasLayers: boolean;
  styles: {
    controls: StyleProp<ViewStyle>;
    controlButton: StyleProp<ViewStyle>;
    activeControlButton?: StyleProp<ViewStyle>;
  };
}

const SvgControls: React.FC<SvgControlsProps> = ({
  onConvertToPng,
  onZoomIn,
  onZoomOut,
  onResetView,
  onRotateLeft,
  onRotateRight,
  onToggleFullscreen,
  onToggleLayerPanel,
  isFullscreen,
  layerPanelVisible,
  hasLayers,
  styles,
}) => {
  return (
    <ScrollView style={styles.controls} horizontal={true} showsHorizontalScrollIndicator={true}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <TouchableOpacity style={styles.controlButton} onPress={onConvertToPng}>
          <Icon name="save-alt" size={24} color="#007AFF" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.controlButton} onPress={onZoomIn}>
          <Icon name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.controlButton} onPress={onZoomOut}>
          <Icon name="remove" size={24} color="#007AFF" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.controlButton} onPress={onResetView}>
          <Icon name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
        {onRotateLeft && (
          <TouchableOpacity style={styles.controlButton} onPress={onRotateLeft}>
            <Icon name="rotate-left" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
        {onRotateRight && (
          <TouchableOpacity style={styles.controlButton} onPress={onRotateRight}>
            <Icon name="rotate-right" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
        <TouchableOpacity style={styles.controlButton} onPress={onToggleFullscreen}>
          <Icon name={isFullscreen ? 'fullscreen-exit' : 'fullscreen'} size={24} color="#007AFF" />
        </TouchableOpacity>
        {hasLayers && (
          <TouchableOpacity
            style={[styles.controlButton, layerPanelVisible && styles.activeControlButton]}
            onPress={onToggleLayerPanel}>
            <Icon name="layers" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
};

export default SvgControls;
