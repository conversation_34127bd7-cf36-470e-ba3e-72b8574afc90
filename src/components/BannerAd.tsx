import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Text, Image, useColorScheme} from 'react-native';
import {
  BannerAd,
  BannerAdSize,
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaAspectRatio,
} from 'react-native-google-mobile-ads';
import {useSelector} from 'react-redux';
import {RootState} from '@/store';

interface TopBannerAdProps {
  bannerId: string;
  nativeId?: string;
}

const AdComponent: React.FC<TopBannerAdProps> = ({bannerId, nativeId}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [nativeAd, setNativeAd] = useState<NativeAd>();
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(!!nativeId);
  const {hasSilver, hasGold} = useSelector((state: RootState) => state.iap);
  const shouldShowAds = !(hasSilver || hasGold);

  useEffect(() => {
    if (!nativeId) return;

    setIsLoading(true);
    setHasError(false);

    try {
      console.log('Creating native ad request for ID:', nativeId);

      NativeAd.createForAdRequest(nativeId, {
        aspectRatio: NativeMediaAspectRatio.LANDSCAPE,
      })
        .then(ad => {
          setNativeAd(ad);
          setHasError(false);
          console.log('Native ad loaded successfully for ID:', nativeId);
        })
        .catch(error => {
          console.error('Failed to load native ad:', error);
          setHasError(true);
        })
        .finally(() => {
          setIsLoading(false);
          console.log('Native ad request completed for ID:', nativeId);
        });
    } catch (error) {
      console.error('Error creating native ad request:', error);
      setHasError(true);
      setIsLoading(false);
    }
  }, [nativeId]);

  // If user has purchased a package, don't show ads
  if (!shouldShowAds) {
    return null;
  }

  console.log(
    'BannerAdComponent rendered with nativeId:',
    nativeId,
    'isLoading:',
    isLoading,
    'hasError:',
    hasError,
    hasSilver,
    hasGold,
  );

  // If native ad failed or no nativeId provided, show banner ad
  if (isLoading || hasError || !nativeAd || !nativeId) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: isDarkMode ? '#1E1E1E' : '#FFFFFF',
            borderBottomColor: isDarkMode ? '#444' : '#E0E0E0',
          },
        ]}>
        <BannerAd unitId={bannerId} size={BannerAdSize.BANNER} />
      </View>
    );
  }

  // Show native ad - ALL assets must be inside NativeAdView
  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? '#1E1E1E' : '#FFFFFF',
          borderBottomColor: isDarkMode ? '#444' : '#E0E0E0',
        },
      ]}>
      <NativeAdView
        nativeAd={nativeAd}
        style={[
          styles.nativeAdContainer,
          {
            borderColor: isDarkMode ? '#444' : '#E0E0E0',
          },
        ]}>
        <View style={styles.nativeAdContent}>
          {nativeAd.icon && (
            <NativeAsset assetType={NativeAssetType.ICON}>
              <Image source={{uri: nativeAd.icon.url}} style={styles.icon} />
            </NativeAsset>
          )}
          <View style={styles.titleContainer}>
            <NativeAsset assetType={NativeAssetType.HEADLINE}>
              <Text
                style={[styles.headline, {color: isDarkMode ? '#FFFFFF' : '#000000'}]}
                numberOfLines={1}>
                {nativeAd.headline}
              </Text>
            </NativeAsset>
          </View>
          <View
            style={[
              styles.sponsoredLabel,
              {
                backgroundColor: isDarkMode ? 'rgba(79, 70, 229, 0.3)' : 'rgba(79, 70, 229, 0.2)',
              },
            ]}>
            <Text style={[styles.adLabelText, {color: isDarkMode ? '#DDDDDD' : '#666666'}]}>
              Ad
            </Text>
          </View>
          {nativeAd.callToAction && (
            <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
              <View style={styles.ctaButton}>
                <Text style={styles.ctaText}>{nativeAd.callToAction}</Text>
              </View>
            </NativeAsset>
          )}
        </View>
      </NativeAdView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    borderBottomWidth: 1,
    paddingVertical: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    // flex: 1,
    // minHeight: 100,
  },
  nativeAdContainer: {
    width: '100%',
    borderRadius: 12,
    borderWidth: 1,
  },
  nativeAdContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    margin: 12,
  },
  icon: {
    width: 24,
    height: 24,
    borderRadius: 6,
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  headline: {
    fontSize: 16,
    fontWeight: '600',
  },
  sponsoredLabel: {
    paddingVertical: 2,
    borderRadius: 12,
  },
  adLabelText: {
    fontSize: 12,
  },
  ctaButton: {
    backgroundColor: '#4F46E5',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 80,
  },
  ctaText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    color: '#FFFFFF',
  },
});

export default AdComponent;
