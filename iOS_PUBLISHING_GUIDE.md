# iOS App Publishing Guide for SvgViewerApp

## Pre-Submission Checklist

### ✅ Completed Items

- [x] iOS project configuration verified
- [x] App icons properly configured (all sizes present)
- [x] Launch screen configured
- [x] Privacy manifest (PrivacyInfo.xcprivacy) in place
- [x] Basic Info.plist configuration complete

### 📋 Remaining Steps

## 1. App Store Connect Preparation

### Create App Listing

1. Log in to [App Store Connect](https://appstoreconnect.apple.com)
2. Create a new app with the following details:
   - **Platform**: iOS
   - **Name**: SvgViewerApp (or your preferred display name)
   - **Primary Language**: English
   - **Bundle ID**: Select your App ID (e.g., com.yourcompany.SvgViewerApp)
   - **SKU**: SvgViewerApp-1.0.0 (unique identifier for your records)

### App Information

- **Category**: Utilities > Graphics & Design
- **Subcategory**: (Optional)
- **Age Rating**: 4+ (No objectionable content)

## 2. App Metadata Preparation

### Required Assets

- **App Icon**: 1024x1024px PNG (already configured in Xcode)
- **Screenshots**:
  - iPhone 6.7-inch (iPhone 15 Pro Max, 16 Pro Max): 1290x2796px
  - iPhone 6.5-inch (iPhone 12/13/14 Pro Max): 1284x2778px
  - iPhone 5.5-inch: 1242x2208px
  - iPad Pro (12.9-inch): 2048x2732px
  - iPad Pro (11-inch): 1668x2388px

### Recommended Screenshot Scenarios

1. Home screen showing file picker
2. SVG file viewing with controls
3. Zoom/pan functionality demonstration
4. Settings/features screen
5. File information display

### App Description

Prepare these text elements:

- **App Name**: SvgViewerApp
- **Subtitle**: (Optional) Professional SVG Viewer
- **Description**:
  "SvgViewerApp is a powerful SVG file viewer for iOS. Open, view, and interact with SVG files with smooth zooming, panning, and professional rendering. Perfect for designers, developers, and anyone working with vector graphics."

- **Keywords**: svg, viewer, vector, graphics, design, illustrator, sketch
- **Support URL**: Your support website
- **Marketing URL**: (Optional) Your marketing website
- **Privacy Policy URL**: Required for apps with data collection

## 3. Code Signing & Certificates

### Required Accounts

- **Apple Developer Account** ($99/year)
- **App Store Connect Access**

### Certificates Needed

1. **Distribution Certificate** (App Store)
2. **App ID** (com.yourcompany.SvgViewerApp)
3. **Provisioning Profile** (App Store distribution)

### Steps to Configure

1. Open Xcode with your project
2. Go to Signing & Capabilities tab
3. Select your team (Apple Developer account)
4. Ensure "Automatically manage signing" is enabled
5. Xcode will automatically create certificates and profiles

## 4. Build Configuration

### Update Version Numbers

In Xcode:

1. Select your project in the navigator
2. Select the target
3. Under "General" tab:
   - **Version**: 1.0.0 (marketing version)
   - **Build**: 1 (internal build number)

### Build Settings

- **Build Configuration**: Release
- **Strip Debug Symbols**: Yes
- **Optimization Level**: Fastest, Smallest [-Os]

## 5. Archive and Upload

### Build for Distribution

```bash
# Clean build folder
xcodebuild clean -workspace ios/SvgViewerApp.xcworkspace -scheme SvgViewerApp

# Create archive
xcodebuild archive -workspace ios/SvgViewerApp.xcworkspace -scheme SvgViewerApp -configuration Release -archivePath ./build/SvgViewerApp.xcarchive

# Export for App Store
xcodebuild -exportArchive -archivePath ./build/SvgViewerApp.xcarchive -exportOptionsPlist ExportOptions.plist -exportPath ./build
```

### Using Xcode GUI

1. Product > Archive
2. Wait for archive to complete
3. Click "Distribute App"
4. Select "App Store Connect"
5. Choose "Upload"
6. Follow the signing process

## 6. App Store Connect Submission

### After Upload

1. Go to App Store Connect > My Apps
2. Select your app
3. Under "Build" section, select the uploaded build
4. Complete all required metadata:
   - App Review Information
   - Version Release (Automatic/Manual)
   - Contact Information

### App Review Information

- **Demo Account**: (If required)
- **Notes**: "This is an SVG file viewer app. No special configuration needed for review. Test with any SVG file."

## 7. Pricing and Availability

### Pricing

- **Price Tier**: Free (or choose your price)
- **In-App Purchases**: Configure if you have any

### Availability

- Select countries/regions
- Choose release date (immediate or scheduled)

## 8. Post-Submission

### Review Process

- Typically takes 24-48 hours
- Monitor email for status updates
- Be prepared to respond to review questions

### Common Rejection Reasons

- Missing privacy descriptions
- Incomplete metadata
- App functionality issues
- Guideline violations

## 9. Release Management

### After Approval

- Monitor crash reports and analytics
- Prepare update releases
- Respond to user reviews

### Version Updates

- Increment version numbers for updates
- Follow same submission process
- Consider TestFlight for beta testing

## Troubleshooting

### Common Issues

1. **Code Signing Errors**: Check certificates in Apple Developer portal
2. **Upload Failures**: Verify internet connection and try again
3. **Metadata Rejections**: Ensure all text follows App Store guidelines
4. **Functionality Rejections**: Test thoroughly before submission

### Resources

- [Apple App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [App Store Connect Help](https://help.apple.com/app-store-connect/)
- [Xcode Documentation](https://developer.apple.com/xcode/)

## Support

For technical support during publishing:

- Apple Developer Support: https://developer.apple.com/support/
- React Native Community: https://reactnative.dev/help

Good luck with your app submission!
